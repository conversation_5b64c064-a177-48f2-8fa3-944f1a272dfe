# Generated by Django 4.2.18 on 2025-05-16 09:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dental", "0006_dentalresult_processed_image"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="dentalresult",
            name="image",
        ),
        migrations.RemoveField(
            model_name="dentalresult",
            name="processed_image",
        ),
        migrations.RemoveField(
            model_name="dentalresult",
            name="processed_images",
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="image_path",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="原始图像路径"
            ),
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="processed_image_path",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="处理后的图像路径"
            ),
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="processed_images_data",
            field=models.JSONField(blank=True, null=True, verbose_name="处理后的图像数据"),
        ),
    ]
