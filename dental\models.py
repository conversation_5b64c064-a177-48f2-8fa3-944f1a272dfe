from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from django.conf import settings
from django.contrib.auth import get_user_model

from django.urls import reverse



class CustomUserManager(BaseUserManager):
    def create_user(self, username, password=None, **extra_fields):
        if not username:
            raise ValueError('用户必须有用户名')
        user = self.model(username=username, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置 is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置 is_superuser=True')

        return self.create_user(username, password, **extra_fields)

class CustomUser(AbstractBaseUser):
    id = models.AutoField(primary_key=True)
    email = models.EmailField(unique=True)
    username = models.CharField(max_length=150, unique=True)
    password = models.CharField(max_length=128)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    last_login = models.DateTimeField(null=True, blank=True)

    objects = CustomUserManager()

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.username

    def has_perm(self, perm, obj=None):
        return self.is_superuser

    def has_module_perms(self, app_label):
        return self.is_superuser

User = get_user_model()

class DentalResult(models.Model):
    """牙齿检测结果模型"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="关联用户",
        db_column='user_id',
        null=True,
        blank=True
    )

    cavity_ratio = models.FloatField(verbose_name="龋洞比例", default=0)
    crack_ratio = models.FloatField(verbose_name="裂缝比例", default=0)
    caries_ratio = models.FloatField(verbose_name="蛀牙比例", default=0)
    total_decay_ratio = models.FloatField(verbose_name="总蛀牙问题比例", default=0)
    plaque_ratio = models.FloatField(verbose_name="牙菌斑比例", default=0)
    health_score = models.CharField(max_length=50, verbose_name="健康评分")
    cleanliness_score = models.CharField(max_length=50, verbose_name="清洁度评分")
    is_stream_result = models.BooleanField(default=False, verbose_name="是否实时检测结果")
    frame_count = models.IntegerField(default=0, verbose_name="检测帧数")
    duration = models.FloatField(default=0, verbose_name="检测时长(秒)")

    # 图像相关字段
    image = models.ImageField(
        upload_to='uploads/',
        verbose_name="原始图像",
        null=True,
        blank=True
    )
    processed_images = models.TextField(
        verbose_name="处理后的图像(Base64)",
        null=True,
        blank=True
    )

    # 时间戳
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="创建时间"
    )

    class Meta:
        verbose_name = "牙齿检测结果"
        verbose_name_plural = "牙齿检测结果"
        ordering = ['-created_at']
        db_table = 'dental_results'

    def __str__(self):
        return f"{self.user.username if self.user else '匿名用户'}的检测结果 - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def get_absolute_url(self):
        return reverse('dental-result-detail', kwargs={'pk': self.pk})


from django.db.models.signals import post_save
from django.dispatch import receiver


@receiver(post_save, sender=DentalResult)
def limit_dental_results(sender, instance, created, **kwargs):
    if created and instance.user:  # 只处理新创建的且有用户的记录
        # 获取该用户的所有结果并按创建时间排序
        user_results = DentalResult.objects.filter(user=instance.user).order_by('-created_at')

        # 如果超过7条，删除最旧的记录
        if user_results.count() > 7:
            # 获取第7条之后的所有记录ID
            ids_to_delete = user_results.values_list('id', flat=True)[7:]
            # 批量删除
            DentalResult.objects.filter(id__in=ids_to_delete).delete()


#验证码
class VerificationCode(models.Model):
    email = models.EmailField(unique=True)  # 邮箱地址
    verificationCode = models.CharField(max_length=6)  # 验证码
    created_at = models.DateTimeField(auto_now_add=True)  # 创建时间

    def __str__(self):
        return f'{self.email} - {self.verificationCode}'
# Create your models here.

from django.utils import timezone






# class ImageModel(models.Model):
#     image = models.ImageField(upload_to='images/')





class EducationContent(models.Model):
    CONTENT_TYPES = [
        ('CARE_TIPS', '科学护牙指南'),
        ('DENTAL_FACTS', '牙齿健康知识'),
    ]

    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES)
    title = models.CharField(max_length=100)
    icon = models.CharField(max_length=10, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    content = models.TextField(blank=True, null=True)
    details = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    source_file = models.CharField(max_length=255, blank=True, null=True)  # 新增字段，记录来源文件
    file_timestamp = models.DateTimeField(blank=True, null=True)  # 新增字段，记录文件修改时间

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['content_type']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.get_content_type_display()}: {self.title}"


from django.contrib import admin
from .models import EducationContent, CustomUser, DentalResult


@admin.register(EducationContent)
class EducationContentAdmin(admin.ModelAdmin):
    list_display = ('title', 'content_type', 'created_at', 'is_active')
    list_filter = ('content_type', 'is_active')
    search_fields = ('title', 'description')
    list_editable = ('is_active',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('content_type', 'title', 'is_active')
        }),
        ('内容详情', {
            'fields': ('icon', 'description', 'content', 'details'),
            'classes': ('wide',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
