# Generated by Django 4.2.18 on 2025-03-30 07:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("dental", "0002_alter_dentalresult_options"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="dentalresult",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "牙齿检测结果",
                "verbose_name_plural": "牙齿检测结果",
            },
        ),
        migrations.RemoveField(
            model_name="dentalresult",
            name="caries_ratio",
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="crack_ratio",
            field=models.FloatField(
                default=0, help_text="检测到的裂缝面积占牙齿总面积的比例", verbose_name="裂缝比例"
            ),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="cavity_ratio",
            field=models.FloatField(
                default=0, help_text="检测到的龋洞面积占牙齿总面积的比例", verbose_name="龋洞比例"
            ),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="cleanliness_score",
            field=models.CharField(default="未检测", max_length=50, verbose_name="清洁度评分"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="health_score",
            field=models.CharField(default="未检测", max_length=50, verbose_name="健康评分"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="image",
            field=models.ImageField(
                blank=True, null=True, upload_to="uploads/", verbose_name="原始图像"
            ),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="plaque_ratio",
            field=models.FloatField(
                default=0, help_text="检测到的牙菌斑面积占牙齿总面积的比例", verbose_name="牙菌斑比例"
            ),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="processed_images",
            field=models.TextField(
                blank=True, null=True, verbose_name="处理后的图像(Base64)"
            ),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="user",
            field=models.ForeignKey(
                blank=True,
                db_column="user_id",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="关联用户",
            ),
        ),
        migrations.AlterModelTable(
            name="dentalresult",
            table="dental_results",
        ),
    ]
