# education/tasks.py
from celery import shared_task
from django.utils import timezone
from .models import EducationContent



@shared_task
def refresh_education_content():
    # 每隔一段时间自动刷新内容
    for content_type in ['careTips', 'dentalFacts']:
        # 检查最近是否有更新
        last_update = EducationContent.objects.filter(
            content_type=content_type.upper()
        ).order_by('-created_at').first()

