# dental/middleware.py
import time
import logging
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)


class StreamMonitoringMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 只监控特定路径的请求
        if request.path in ('/proxy/esp32/stream', '/stop_and_calculate/'):
            try:
                cache_data = cache.get(settings.REALTIME_DATA_CACHE_KEY) or {}
                if cache_data.get('active'):
                    # 检查是否超时（5分钟无更新）
                    if time.time() - cache_data.get('last_update', 0) > 300:
                        logger.warning(
                            f"Inactive stream detected - IP: {cache_data.get('esp_ip')}, "
                            f"Last update: {cache_data.get('last_update')}"
                        )
                        cache_data['active'] = False
                        cache.set(settings.REALTIME_DATA_CACHE_KEY, cache_data, 300)

            except Exception as e:
                logger.error(f"Stream monitoring error: {str(e)}", exc_info=True)

        response = self.get_response(request)
        return response