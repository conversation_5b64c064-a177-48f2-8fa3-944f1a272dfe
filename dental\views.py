import queue
import threading

import cv2
import serial
from django.contrib.auth import get_user_model
from serial.tools import list_ports

from . import udp_server

User = get_user_model()

import numpy as np
import io
import os
import base64
import random
import time
from PIL import Image
from django.core.mail import send_mail
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.core.cache import cache
from .dental_utils import yolov8_seg_inference, calculate_dental_health, calculate_health_score, \
    calculate_cleanliness_score
from django.views.decorators.http import require_GET
from .models import DentalResult, VerificationCode
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.conf import settings
import logging
import re

REALTIME_DATA_CACHE_KEY = 'realtime_dental_data'
BLUETOOTH_DATA_QUEUE = queue.Queue()  # 用于存储蓝牙接收的数据
logger = logging.getLogger(__name__)
User = get_user_model()
active_streams = {}

SERIAL_CONNECTION = None  # 保持串口连接引用



def init_bluetooth_receiver(port=None, baudrate=115200, auto_reconnect=True):
    """
    增强版蓝牙/串口接收器初始化
    参数:
        port: 指定端口(如'COM3')，None则自动检测
        baudrate: 波特率，默认115200
        auto_reconnect: 是否自动重连，默认True
    """
    global SERIAL_CONNECTION

    if serial is None:
        logger.error("pyserial 不可用，启动备用模式")
        start_fallback_mode()
        return

    def find_serial_device():
        """自动检测可能的串口设备"""
        device_priority = [
            {'match': 'CH340', 'type': 'USB-UART', 'score': 100},
            {'match': 'CP210', 'type': 'USB-UART', 'score': 90},
            {'match': 'FTDI', 'type': 'USB-UART', 'score': 80},
            {'match': '蓝牙', 'type': 'Bluetooth', 'score': 70},
            {'match': 'Bluetooth', 'type': 'Bluetooth', 'score': 60}
        ]

        available_ports = []
        for p in list_ports.comports():
            for criteria in device_priority:
                if criteria['match'] in p.description:
                    available_ports.append({
                        'port': p.device,
                        'type': criteria['type'],
                        'description': p.description,
                        'score': criteria['score']
                    })
                    break
        return sorted(available_ports, key=lambda x: -x['score'])

    def test_serial_port(port):
        """测试端口是否可用"""
        try:
            ser = serial.Serial(
                port=port,
                baudrate=baudrate,
                timeout=1,
                write_timeout=1,
                inter_byte_timeout=0.5
            )
            # 简单握手测试
            ser.write(b'AT\r\n')
            if ser.read(4) == b'OK\r\n':
                return ser
            ser.close()
        except Exception as e:
            logger.debug(f"端口测试失败 {port}: {str(e)}")
        return None

    def start_receiver_thread(serial_conn):
        """启动数据接收线程"""
        def receiver_worker():
            logger.info(f"开始接收数据线程，端口: {serial_conn.port}")
            while getattr(serial_conn, 'is_open', False):
                try:
                    # 数据接收逻辑
                    if serial_conn.in_waiting:
                        data = serial_conn.read(serial_conn.in_waiting)
                        BLUETOOTH_DATA_QUEUE.put({
                            'timestamp': time.time(),
                            'data': data,
                            'source': f'serial:{serial_conn.port}'
                        })
                    time.sleep(0.1)
                except Exception as e:
                    logger.error(f"接收线程错误: {str(e)}")
                    break

            if auto_reconnect:
                logger.info("尝试重新连接...")
                time.sleep(5)
                init_bluetooth_receiver(port=serial_conn.port)

        thread = threading.Thread(
            target=receiver_worker,
            name=f"SerialReceiver-{serial_conn.port}",
            daemon=True
        )
        thread.start()
        return thread

    # === 主逻辑 ===
    try:
        # 关闭现有连接
        if SERIAL_CONNECTION and SERIAL_CONNECTION.is_open:
            SERIAL_CONNECTION.close()

        # 尝试指定端口
        if port:
            ser = test_serial_port(port)
            if ser:
                SERIAL_CONNECTION = ser
                return start_receiver_thread(ser)

        # 自动检测设备
        devices = find_serial_device()
        for dev in devices:
            ser = test_serial_port(dev['port'])
            if ser:
                SERIAL_CONNECTION = ser
                return start_receiver_thread(ser)

        # 尝试常见端口
        common_ports = ['COM3', 'COM4', 'COM5', 'COM6']
        for p in common_ports:
            if any(d['port'] == p for d in devices):
                continue
            ser = test_serial_port(p)
            if ser:
                SERIAL_CONNECTION = ser
                return start_receiver_thread(ser)

        raise RuntimeError("未找到可用串口设备")

    except Exception as e:
        logger.error(f"初始化失败: {str(e)}")
        start_fallback_mode()

def start_fallback_mode():
    """启动备用通信模式"""
    logger.warning("切换到备用通信模式")
    BLUETOOTH_DATA_QUEUE.put({
        'timestamp': time.time(),
        'source': 'fallback',
        'status': 'no_serial_available'
    })

def close_bluetooth_receiver():
    """安全关闭接收器"""
    global SERIAL_CONNECTION
    if SERIAL_CONNECTION:
        try:
            SERIAL_CONNECTION.close()
        except:
            pass
    SERIAL_CONNECTION = None
# 初始化时启动蓝牙接收
init_bluetooth_receiver()


def init_realtime_cache():
    return {
        'start_time': time.time(),
        'frame_count': 0,
        'decay_ratios': [],
        'plaque_ratios': [],
        'last_update': time.time(),
        'active': True,
        'source': 'bluetooth'  # 标记数据来源
    }


@csrf_exempt
def start_stream(request):
    """激活设备视频流"""
    if request.method == 'POST':
        try:
            # 初始化实时数据缓存
            cache_data = init_realtime_cache()
            cache.set(REALTIME_DATA_CACHE_KEY, cache_data, timeout=3600)

            return JsonResponse({
                'status': 'streaming_started',
                'source': 'bluetooth',
                'timestamp': int(time.time())
            })

        except Exception as e:
            logger.error(f"启动流失败: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': '仅支持POST请求'}, status=405)


@csrf_exempt
def stop_stream(request):
    """停止分析并返回结果"""
    if request.method == 'POST':
        try:
            cache_data = cache.get(REALTIME_DATA_CACHE_KEY)
            if not cache_data or not cache_data.get('active'):
                return JsonResponse({'error': '没有活跃的视频流'}, status=404)

            # 计算平均健康指标
            decay_avg = sum(cache_data['decay_ratios']) / max(1, cache_data['frame_count'])
            plaque_avg = sum(cache_data['plaque_ratios']) / max(1, cache_data['frame_count'])

            return JsonResponse({
                'status': 'analysis_complete',
                'duration_sec': round(time.time() - cache_data['start_time'], 1),
                'frame_count': cache_data['frame_count'],
                'decay_ratio': round(decay_avg, 4),
                'plaque_ratio': round(plaque_avg, 4),
                'health_score': calculate_health_score(decay_avg),
                'cleanliness_score': calculate_cleanliness_score(plaque_avg)
            })

        except Exception as e:
            logger.error(f"停止流失败: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': '仅支持POST请求'}, status=405)


@csrf_exempt
def video_feed(request):
    """提供实时视频流"""

    def frame_generator():
        while True:
            try:
                # 从蓝牙队列获取数据
                frame_data = BLUETOOTH_DATA_QUEUE.get(timeout=1)

                # 直接发送JPEG数据
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n'
                       + frame_data['data'] + b'\r\n')

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Frame generation error: {str(e)}")
                break

    return StreamingHttpResponse(
        frame_generator(),
        content_type='multipart/x-mixed-replace; boundary=frame'
    )
@csrf_exempt
def upload_image(request):
    if request.method == "POST":
        try:
            # 1. 解析JSON请求体
            try:
                data = json.loads(request.body)
                image_base64 = data.get('image')
                if not image_base64:
                    return JsonResponse({
                        'code': 1,
                        'message': '未提供图像数据'
                    }, status=400)
            except json.JSONDecodeError:
                return JsonResponse({
                    'code': 1,
                    'message': '无效的JSON格式'
                }, status=400)

            # 2. 获取用户信息（如果已认证）
            user = request.user if request.user.is_authenticated else None

            # 3. 解码Base64图像数据
            try:
                # 处理可能带有data:image/png;base64,前缀的情况
                if 'base64,' in image_base64:
                    image_base64 = image_base64.split('base64,')[1]

                image_bytes = base64.b64decode(image_base64)
                image_io = io.BytesIO(image_bytes)
                image = Image.open(image_io)

                # 转换图片模式为RGB（如果原始是RGBA）
                if image.mode == 'RGBA':
                    image = image.convert('RGB')

            except Exception as e:
                logger.error(f"图像解码失败: {str(e)}", exc_info=True)
                return JsonResponse({
                    'code': 1,
                    'message': f'图像解码失败: {str(e)}'
                }, status=400)

            # 4. 保存原始图像
            timestamp = int(time.time())
            image_filename = f'upload_{timestamp}.jpg' if user else f'anonymous_{timestamp}.jpg'
            image_path = os.path.join('media/uploads', image_filename)

            try:
                os.makedirs(os.path.dirname(image_path), exist_ok=True)
                image.save(image_path, 'JPEG', quality=95)
            except Exception as e:
                logger.error(f"保存图像失败: {str(e)}", exc_info=True)
                return JsonResponse({
                    'code': 1,
                    'message': f'保存图像失败: {str(e)}'
                }, status=500)

            # 5. 使用YOLO模型处理图像
            model_path = os.path.join(settings.BASE_DIR, 'best.pt')
            if not os.path.exists(model_path):
                logger.error(f"模型文件未找到: {model_path}")
                return JsonResponse({
                    'code': 1,
                    'message': f'模型文件未找到: {model_path}'
                }, status=500)

            try:
                results = yolov8_seg_inference(model_path, image_path)
            except Exception as e:
                logger.error(f"模型推理失败: {str(e)}", exc_info=True)
                return JsonResponse({
                    'code': 1,
                    'message': f'模型处理失败: {str(e)}'
                }, status=500)

            # 6. 保存处理后的图像
            base64_strings = []
            output_dir = os.path.join(settings.MEDIA_ROOT, 'processed_images')
            os.makedirs(output_dir, exist_ok=True)

            # 清除之前处理过的图像
            for f in os.listdir(output_dir):
                if f.startswith('processed_'):
                    try:
                        os.remove(os.path.join(output_dir, f))
                    except Exception as e:
                        logger.warning(f"删除旧文件失败: {f}, {str(e)}")

            # 保存新处理过的图像
            for i, result in enumerate(results):
                filename = f"processed_{timestamp}_{i}.png"
                filepath = os.path.join(output_dir, filename)

                try:
                    result.save(filepath)
                    with open(filepath, "rb") as file:
                        base64_strings.append(base64.b64encode(file.read()).decode('utf-8'))
                except Exception as e:
                    logger.error(f"保存处理结果失败: {str(e)}", exc_info=True)
                    return JsonResponse({
                        'code': 1,
                        'message': f'无法保存处理后的图像: {str(e)}'
                    }, status=500)

            # 7. 计算牙齿健康数据
            try:
                health_data = calculate_dental_health(results)
                if 'error' in health_data:
                    logger.error(f"健康计算错误: {health_data['error']}")
                    return JsonResponse({
                        'code': 1,
                        'message': health_data['error']
                    }, status=500)
            except Exception as e:
                logger.error(f"健康计算异常: {str(e)}", exc_info=True)
                return JsonResponse({
                    'code': 1,
                    'message': f'健康计算失败: {str(e)}'
                }, status=500)

            # 8. 保存到数据库
            try:
                dental_result = DentalResult(
                    user=user,
                    image=image_path,
                    processed_images=json.dumps(base64_strings),
                    cavity_ratio=health_data['cavity_ratio'],
                    crack_ratio=health_data['crack_ratio'],
                    caries_ratio=health_data['caries_ratio'],
                    total_decay_ratio=health_data['total_decay_ratio'],
                    plaque_ratio=health_data['plaque_ratio'],
                    health_score=health_data['health_score'],
                    cleanliness_score=health_data['cleanliness_score'],
                )
                dental_result.save()
            except Exception as e:
                logger.error(f"数据库保存失败: {str(e)}", exc_info=True)
                return JsonResponse({
                    'code': 1,
                    'message': f'保存结果失败: {str(e)}'
                }, status=500)

            # 9. 更新缓存
            cache.set('cavity_ratio', f"{health_data['cavity_ratio']:.2%}", 3600)
            cache.set('crack_ratio', f"{health_data['crack_ratio']:.2%}", 3600)
            cache.set('caries_ratio', f"{health_data['caries_ratio']:.2%}", 3600)
            cache.set('total_decay_ratio', f"{health_data['total_decay_ratio']:.2%}", 3600)
            cache.set('plaque_ratio', f"{health_data['plaque_ratio']:.2%}", 3600)
            cache.set('health_score', health_data['health_score'], 3600)
            cache.set('cleanliness_score', health_data['cleanliness_score'], 3600)

            # 10. 返回成功响应
            return JsonResponse({
                'code': 0,
                'message': '图像处理成功',
                'cavity_ratio': f"{health_data['cavity_ratio']:.2%}",
                'crack_ratio': f"{health_data['crack_ratio']:.2%}",
                'caries_ratio': f"{health_data['caries_ratio']:.2%}",
                'total_decay_ratio': f"{health_data['total_decay_ratio']:.2%}",
                'plaque_ratio': f"{health_data['plaque_ratio']:.2%}",
                'health_score': health_data['health_score'],
                'cleanliness_score': health_data['cleanliness_score'],
                'images': base64_strings
            })

        except Exception as e:
            logger.error(f"未处理的异常: {str(e)}", exc_info=True)
            return JsonResponse({
                'code': 1,
                'message': f'服务器内部错误: {str(e)}'
            }, status=500)

    return JsonResponse({
        'code': 1,
        'message': '仅支持POST请求'
    }, status=405)



@csrf_exempt
def get_result(request):
    # 从缓存获取所有需要的字段
    cache_data = {
        'cavity_ratio': cache.get('cavity_ratio', '未检测'),
        'caries_ratio': cache.get('caries_ratio', '未检测'),
        'crack_ratio': cache.get('crack_ratio', '未检测'),
        'total_decay_ratio': cache.get('total_decay_ratio', '未检测'),  # 新增
        'plaque_ratio': cache.get('plaque_ratio', '未检测'),
        'health_score': cache.get('health_score', '未检测'),
        'cleanliness_score': cache.get('cleanliness_score', '未检测')
    }

    # 如果缓存中有数据
    if cache_data['cavity_ratio'] != '未检测':
        return JsonResponse({
            'code': 0,
            'message': 'Success',
            **cache_data  # 展开所有缓存数据
        })

    # 从数据库获取最新记录
    try:
        if request.user.is_authenticated:
            latest_result = DentalResult.objects.filter(user=request.user).latest('created_at')
        else:
            latest_result = DentalResult.objects.latest('created_at')

        return JsonResponse({
            'code': 0,
            'message': 'Success',
            'cavity_ratio': f"{latest_result.cavity_ratio:.2%}",
            'caries_ratio': f"{latest_result.caries_ratio:.2%}",
            'crack_ratio': f"{latest_result.crack_ratio:.2%}",
            'total_decay_ratio': f"{latest_result.total_decay_ratio:.2%}",  # 新增
            'plaque_ratio': f"{latest_result.plaque_ratio:.2%}",
            'health_score': latest_result.health_score,
            'cleanliness_score': latest_result.cleanliness_score
        })
    except DentalResult.DoesNotExist:
        return JsonResponse({
            'code': 1,
            'message': '请先上传图片进行检测',
            'cavity_ratio': '',
            'caries_ratio': '',
            'crack_ratio': '',
            'total_decay_ratio': '',  # 新增
            'plaque_ratio': '',
            'health_score': '未检测',
            'cleanliness_score': '未检测'
        })


import json
import requests
from django.http import JsonResponse, StreamingHttpResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt


@csrf_exempt
def ai_chat(request):
    try:
        # 1. 参数验证
        if request.method != 'POST':
            return JsonResponse({'error': 'Only POST allowed'}, status=405)

        # 2. 解析JSON数据（带错误处理）
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '')
            if not user_message:
                return JsonResponse({'error': 'Message required'}, status=400)
        except Exception as e:
            return JsonResponse({'error': f'Invalid JSON: {str(e)}'}, status=400)

        # 3. 调用DeepSeek API（带超时和重试）
        deepseek_api_url = "https://api.siliconflow.cn/v1/chat/completions"
        api_key = "sk-jxjjtuwgssuxnsbzbutueyvpkwhcbjcqdcgfjecelcwnlgvg"  # 建议移到settings.py

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        payload = {
            # 'model': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            'model': 'THUDM/GLM-4-9B-0414',
            'messages': [{'role': 'user', 'content': user_message}],
            'temperature': 0.7,
            'max_tokens': 1000
        }

        try:
            response = requests.post(
                deepseek_api_url,
                headers=headers,
                json=payload,
                timeout=30  # 增加超时时间
            )
            response.raise_for_status()  # 自动处理4xx/5xx错误
            result = response.json()

            # 4. 验证响应格式
            if not result.get('choices'):
                raise ValueError("Invalid API response format")

            reply = result['choices'][0]['message']['content']
            return JsonResponse({'response': reply})

        except requests.exceptions.RequestException as e:
            return JsonResponse({'error': f'API request failed: {str(e)}'}, status=502)

    except Exception as e:
        logger.error(f"AI chat error: {str(e)}", exc_info=True)  # 记录完整错误堆栈
        return JsonResponse({'error': 'Internal server error'}, status=500)




@require_GET
    # @login_required
def get_dental_data(request):
    # 获取当前用户的牙齿健康记录
    # user = request.user
    # records = DentalResult.objects.filter(user=user).order_by('created_at')

    records = DentalResult.objects.all().order_by('created_at')
    dates = [record.created_at.strftime('%Y-%m-%d') for record in records]
    caries_ratios = [record.caries_ratio for record in records]

    # 构造返回的 JSON 数据
    data = {
        'dates': dates,
        'caries_ratios': caries_ratios,
    }

    # 返回 JSON 响应
    return JsonResponse(data)




def is_valid_base64(s):
    """
    验证字符串是否是有效的 Base64 编码
    """
    if not isinstance(s, str):
        return False

    # 移除 data:image 前缀（如果有）
    if s.startswith('data:image'):
        parts = s.split(',')
        if len(parts) != 2:
            return False
        s = parts[1]

    # 检查 Base64 正则表达式
    base64_regex = r'^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$'
    if not re.match(base64_regex, s):
        return False

    try:
        # 尝试解码验证（不验证内容是否合法图片）
        if isinstance(s, str):
            s_bytes = s.encode('ascii')
        base64.b64decode(s_bytes, validate=True)
        return True
    except Exception:
        return False


@csrf_exempt
def get_dental_records(request):
    try:
        # 获取最近7条记录
        records = DentalResult.objects.all().order_by('-created_at')[:7]
        records_data = []

        for record in records:
            # 处理 processed_images 字段
            processed_images = []
            if record.processed_images:
                try:
                    # 尝试解析 JSON 字符串
                    image_list = json.loads(record.processed_images)
                    if isinstance(image_list, list):
                        # 验证每个 Base64 编码的图像
                        for img in image_list:
                            if isinstance(img, str) and is_valid_base64(img):
                                processed_images.append(img)
                            else:
                                # 记录无效的 Base64 数据
                                print(f"Invalid or non-string Base64 image data in record {record.id}: {img}")
                except json.JSONDecodeError:
                    # 记录 JSON 解析错误
                    print(f"Failed to decode processed_images for record {record.id}: {record.processed_images}")

            # 构建记录数据
            record_data = {
                'id': record.id,
                'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'health_score': record.health_score,
                'caries_ratio': f"{record.caries_ratio:.2%}" if record.caries_ratio is not None else "0%",
                'cavity_ratio': f"{record.cavity_ratio:.2%}" if record.cavity_ratio is not None else "0%",
                'total_decay_ratio': f"{record.total_decay_ratio:.2%}" if record.total_decay_ratio is not None else "0%",
                'plaque_ratio': f"{record.plaque_ratio:.2%}" if record.plaque_ratio is not None else "0%",
                'cleanliness_score': record.cleanliness_score,
                'img': record.image.url if record.image else None,  # 添加图片字段
                'processed_images': processed_images  # 添加处理后的图像字段（仅包含有效的 Base64 数据）
            }

            records_data.append(record_data)

        return JsonResponse({'success': True, 'records': records_data})

    except Exception as e:
        # 记录完整错误堆栈
        print(f"Error in get_dental_records: {str(e)}")  # 生产环境应使用日志系统
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


#注册登录功能
@csrf_exempt
def send_verification_code(request):
    if request.method == 'POST':
        try:
            email = request.POST.get('email')
            if not email:
                return JsonResponse({'success': False, 'message': '邮箱地址不能为空'})

            # 生成 6 位随机验证码
            verification_code = ''.join(random.choices('0123456789', k=6))

            # 将验证码存入数据库
            VerificationCode.objects.update_or_create(
                email=email,
                defaults={'verificationCode': verification_code}
            )

            # 发送邮件
            result = send_mail(
                '您的验证码',  # 邮件主题
                f'您的验证码是：{verification_code}',  # 邮件正文
                '<EMAIL>',  # 发件人邮箱
                [email],  # 收件人邮箱列表
                fail_silently=False,
            )
            if result == 1:
                return JsonResponse({'success': True, 'message': '验证码已发送'})
            else:
                return JsonResponse({'success': False, 'message': '邮件发送失败'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'发送验证码失败：{str(e)}'})
    else:
        return JsonResponse({'success': False, 'message': '无效的请求方法'})


@csrf_exempt
def register(request):
    if request.method == 'POST':
        try:
            # Get the user model
            User = get_user_model()

            # Get form data
            username = request.POST.get('username')
            email = request.POST.get('email')
            verification_code = request.POST.get('verificationCode')
            password = request.POST.get('password')

            # Debug prints (remove in production)
            print(f'Registration attempt - Username: {username}, Email: {email}')

            # Validate required fields
            if not all([username, email, verification_code, password]):
                return JsonResponse({
                    'success': False,
                    'message': '所有字段不能为空'
                }, status=400)

            # Verify email format
            if '@' not in email or '.' not in email:
                return JsonResponse({
                    'success': False,
                    'message': '邮箱格式不正确'
                }, status=400)

            # Check verification code
            try:
                code_record = VerificationCode.objects.get(email=email)
                if code_record.verificationCode != verification_code:
                    return JsonResponse({
                        'success': False,
                        'message': '验证码错误'
                    }, status=400)

                # Check if code is expired (e.g., 10 minutes)
                if (timezone.now() - code_record.created_at).seconds > 600:
                    return JsonResponse({
                        'success': False,
                        'message': '验证码已过期'
                    }, status=400)

            except VerificationCode.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '请先获取验证码'
                }, status=400)

            # Check if username exists
            if User.objects.filter(username=username).exists():
                return JsonResponse({
                    'success': False,
                    'message': '用户名已存在'
                }, status=400)

            # Check if email exists
            if User.objects.filter(email=email).exists():
                return JsonResponse({
                    'success': False,
                    'message': '邮箱已注册'
                }, status=400)

            # Create user
            try:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password
                )

                # Optionally log the user in immediately
                # from django.contrib.auth import login
                # login(request, user)

                return JsonResponse({
                    'success': True,
                    'message': '注册成功',
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email
                    }
                })

            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'创建用户失败: {str(e)}'
                }, status=500)

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': '仅支持POST请求'
    }, status=405)


from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth import get_user_model


@csrf_exempt
def user_login(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')

        User = get_user_model()
        try:
            user = User.objects.get(username=username)
            if user.check_password(password):
                return JsonResponse({'success': True, 'user': {'username': user.username}})
            else:
                return JsonResponse({'success': False, 'message': '密码错误'})
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'message': '用户名不存在'})
    return JsonResponse({'success': False, 'message': '仅支持 POST 请求'})
















#
from django.core.cache import cache
from .models import EducationContent

#python manage.py import_education_content
@csrf_exempt
def get_education_content(request):
    # 参数检查
    content_type = request.GET.get('type') or request.POST.get('type')
    if content_type not in ['careTips', 'dentalFacts']:
        return JsonResponse({'error': 'Invalid type'}, status=400)

    # 数据库查询（确保字段名匹配）
    db_type = 'CARE_TIPS' if content_type == 'careTips' else 'DENTAL_FACTS'
    contents = EducationContent.objects.filter(
        content_type=db_type,
        is_active=True
    ).order_by('-created_at')

    # 构造响应
    data = {
        content_type: [
            {
                'title': item.title,
                'icon': item.icon,
                'description': item.description,
                'details': item.details if item.details else [],
                'content': item.content
            }
            for item in contents
        ]
    }
    return JsonResponse(data)