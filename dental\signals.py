# your_app/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import DentalResult

@receiver(post_save, sender=DentalResult)
def limit_dental_results(sender, instance, created, **kwargs):
    if created and instance.user:  # 只处理新创建的且有用户的记录
        # 获取该用户的所有结果并按创建时间排序
        user_results = DentalResult.objects.filter(user=instance.user).order_by('-created_at')

        # 如果超过7条，删除最旧的记录
        if user_results.count() > 7:
            # 获取第7条之后的所有记录ID
            ids_to_delete = user_results.values_list('id', flat=True)[7:]
            # 批量删除
            DentalResult.objects.filter(id__in=ids_to_delete).delete()