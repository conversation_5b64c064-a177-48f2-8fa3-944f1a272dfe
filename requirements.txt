aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
amqp==5.3.1
asgiref @ file:///home/<USER>/feedstock_root/build_artifacts/asgiref_1733215607532/work
async-timeout==5.0.1
attrs==25.3.0
beautifulsoup4==4.13.3
billiard==4.2.1
blinker==1.9.0
celery==5.4.0
certifi==2025.1.31
channels==4.2.2
channels_redis==4.2.1
charset-normalizer==3.4.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
conda-pack @ file:///home/<USER>/feedstock_root/build_artifacts/conda-pack_1734292804024/work
contourpy==1.3.0
cycler==0.12.1
Django @ file:///home/<USER>/feedstock_root/build_artifacts/django_1736955444822/work
django-cors-headers==4.7.0
django-db-connection-pool==1.2.5
django-filter==25.1
django-redis==5.4.0
django-simpleui==2025.1.13
djangorestframework==3.15.2
filelock==3.17.0
Flask==3.1.0
fonttools==4.58.0
frozenlist==1.5.0
fsspec==2025.2.0
greenlet==3.1.1
gunicorn==23.0.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
itsdangerous==2.2.0
Jinja2==3.1.5
kiwisolver==1.4.7
kombu==5.5.1
MarkupSafe==3.0.2
matplotlib==3.9.4
mpmath==1.3.0
msgpack==1.1.0
multidict==6.3.2
mysqlclient==2.2.7
networkx==3.2.1
numpy @ file:///D:/bld/numpy_1707225561314/work/dist/numpy-1.26.4-cp39-cp39-win_amd64.whl#sha256=af5f40857bb7ceb2d3be562fa763863d2f954c104873e89b8ee370f75e047ecf
opencv-python==*********
packaging==25.0
pandas==2.2.3
pillow==11.2.1
prompt_toolkit==3.0.50
propcache==0.3.1
psutil==7.0.0
py-cpuinfo==9.0.0
pyparsing==3.2.3
python-dateutil==2.9.0.post0
pytz==2025.2
PyYAML==6.0.2
redis==5.2.1
requests==2.32.3
scipy==1.13.1
seaborn==0.13.2
six==1.17.0
soupsieve==2.6
SQLAlchemy==2.0.40
sqlparams==6.2.0
sqlparse @ file:///home/<USER>/feedstock_root/build_artifacts/sqlparse_1734007102193/work
sympy==1.13.1
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tqdm==4.67.1
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1733188668063/work
tzdata==2025.1
ultralytics==8.3.134
ultralytics-thop==2.0.14
urllib3==2.4.0
vine==5.1.0
watchdog==6.0.0
wcwidth==0.2.13
Werkzeug==3.1.3
yarl==1.19.0
zhtools==0.2.3
zipp==3.21.0
zope.event==5.0
zope.interface==7.2
