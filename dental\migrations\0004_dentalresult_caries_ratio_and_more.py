# Generated by Django 4.2.18 on 2025-03-30 08:03

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dental", "0003_alter_dentalresult_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="dentalresult",
            name="caries_ratio",
            field=models.FloatField(default=0, verbose_name="蛀牙比例"),
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="total_decay_ratio",
            field=models.FloatField(default=0, verbose_name="总蛀牙问题比例"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="cavity_ratio",
            field=models.FloatField(default=0, verbose_name="龋洞比例"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="cleanliness_score",
            field=models.CharField(max_length=50, verbose_name="清洁度评分"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="crack_ratio",
            field=models.FloatField(default=0, verbose_name="裂缝比例"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="health_score",
            field=models.CharField(max_length=50, verbose_name="健康评分"),
        ),
        migrations.AlterField(
            model_name="dentalresult",
            name="plaque_ratio",
            field=models.FloatField(default=0, verbose_name="牙菌斑比例"),
        ),
    ]
