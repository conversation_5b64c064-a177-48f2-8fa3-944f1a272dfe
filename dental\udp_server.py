import socket
import cv2
import numpy as np
import threading
from datetime import datetime
import os


class UDPFrameReceiver:
    def __init__(self, port=12345):
        self.port = port
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.sock.settimeout(1.0)
        self.frame_buffers = {}  # {client_ip: {frame_id: chunks}}
        self.running = False
        self.lock = threading.Lock()

    def start(self):
        self.sock.bind(('0.0.0.0', self.port))
        self.running = True
        threading.Thread(target=self._receive_loop, daemon=True).start()

    def stop(self):
        self.running = False
        self.sock.close()

    def _receive_loop(self):
        while self.running:
            try:
                data, addr = self.sock.recvfrom(65535)
                client_ip = addr[0]

                # 解析包头 (8字节)
                frame_id = int.from_bytes(data[:4], 'little')
                chunk_num = int.from_bytes(data[4:8], 'little')
                chunk_data = data[8:]

                with self.lock:
                    if client_ip not in self.frame_buffers:
                        self.frame_buffers[client_ip] = {}

                    if frame_id not in self.frame_buffers[client_ip]:
                        self.frame_buffers[client_ip][frame_id] = []

                    self.frame_buffers[client_ip][frame_id].append((chunk_num, chunk_data))

                    # 检查是否收到完整帧
                    chunks = self.frame_buffers[client_ip][frame_id]
                    if len(chunks) > 0:
                        # 假设第一个包包含总块数信息（实际需要更完善的协议）
                        total_chunks = chunks[0][0] if chunk_num == 0 else 0
                        if total_chunks > 0 and len(chunks) == total_chunks:
                            # 组合所有块
                            chunks.sort(key=lambda x: x[0])
                            frame_data = b''.join([c[1] for c in chunks])
                            self._process_frame(frame_data, client_ip)
                            del self.frame_buffers[client_ip][frame_id]

            except socket.timeout:
                continue
            except Exception as e:
                print(f"接收错误: {e}")

    def _process_frame(self, frame_data, client_ip):
        try:
            # 解码JPEG图像
            img = cv2.imdecode(np.frombuffer(frame_data, dtype=np.uint8), cv2.IMREAD_COLOR)
            if img is not None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 保存原始帧（可选）
                os.makedirs("received_frames", exist_ok=True)
                cv2.imwrite(f"received_frames/{client_ip}_{timestamp}.jpg", img)

                # TODO: 调用YOLOv8模型处理
                # results = yolov8_model(img)
                # ...

                # 显示实时画面（开发用）
                cv2.imshow(f"ESP32-CAM {client_ip}", img)
                if cv2.waitKey(1) == 27:  # ESC退出
                    self.stop()

        except Exception as e:
            print(f"帧处理错误: {e}")


if __name__ == "__main__":
    receiver = UDPFrameReceiver()
    receiver.start()

    try:
        while receiver.running:
            pass
    except KeyboardInterrupt:
        receiver.stop()
        cv2.destroyAllWindows()