# Generated by Django 4.2.18 on 2025-03-30 06:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("username", models.CharField(max_length=150, unique=True)),
                ("password", models.CharField(max_length=128)),
                ("is_active", models.BooleanField(default=True)),
                ("is_staff", models.<PERSON>oleanField(default=False)),
                ("is_superuser", models.BooleanField(default=False)),
                ("last_login", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="VerificationCode",
            fields=[
                (
                    "id",
                    models.<PERSON><PERSON><PERSON><PERSON>ield(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("verificationCode", models.CharField(max_length=6)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="EducationContent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content_type",
                    models.CharField(
                        choices=[("CARE_TIPS", "科学护牙指南"), ("DENTAL_FACTS", "牙齿健康知识")],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("icon", models.CharField(blank=True, max_length=10, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("content", models.TextField(blank=True, null=True)),
                ("details", models.JSONField(blank=True, null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "source_file",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("file_timestamp", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["content_type"], name="dental_educ_content_613e65_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="dental_educ_is_acti_30d600_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="DentalResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cavity_ratio", models.FloatField(default=0, verbose_name="裂缝比例")),
                ("caries_ratio", models.FloatField(default=0, verbose_name="蛀牙比例")),
                ("plaque_ratio", models.FloatField(default=0, verbose_name="牙菌斑比例")),
                ("health_score", models.CharField(max_length=50, verbose_name="健康评分")),
                (
                    "cleanliness_score",
                    models.CharField(default="", max_length=50, verbose_name="清洁度评分"),
                ),
                (
                    "image",
                    models.ImageField(blank=True, null=True, upload_to="uploads/"),
                ),
                ("processed_images", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        db_column="user_id",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="关联用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "牙齿检测结果",
                "verbose_name_plural": "牙齿检测结果",
                "db_table": "dental_dentalresult",
                "ordering": ["-created_at"],
            },
        ),
    ]
