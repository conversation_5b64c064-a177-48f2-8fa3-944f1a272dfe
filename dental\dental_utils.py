import time

import numpy as np
import cv2
import torch
from ultralytics import YOL<PERSON>
from collections import deque
from django.core.cache import cache

# 临时存储实时检测数据的缓存键
REALTIME_DATA_CACHE_KEY = 'realtime_dental_data'


def yolov8_seg_inference(model_path, image_path, conf_threshold=0.25, iou_threshold=0.45):
    """YOLOv8 牙齿检测（输出类别调试）"""
    model = YOLO(model_path)
    results = model.predict(source=image_path, conf=conf_threshold, iou=iou_threshold)

    # 调试输出
    for result in results:
        if hasattr(result, 'boxes') and result.boxes is not None:
            print("检测到的类别ID:", result.boxes.cls.cpu().numpy())
            print("模型类别映射:", model.names)

    return results


import cv2
import numpy as np
from ultralytics import YOLO
from typing import Generator, Optional, Union


def yolov8_stream_inference(
        model_path: str,
        stream_source: Union[str, Generator[bytes, None, None], np.ndarray],
        conf_threshold: float = 0.25,
        iou_threshold: float = 0.45,
        device: str = 'cuda:0' if torch.cuda.is_available() else 'cpu',
        frame_size: tuple = (640, 480),
        verbose: bool = False
) -> Generator[tuple, None, None]:
    """
    增强版YOLOv8实时流牙齿检测与分割，支持实时数据收集
    """
    try:
        # 初始化模型
        model = YOLO(model_path)
        model.to(device)
        model.fuse()

        # 初始化实时数据收集
        frame_count = 0
        decay_ratios = []
        plaque_ratios = []

        # 根据输入类型创建视频捕获
        if isinstance(stream_source, str):
            cap = cv2.VideoCapture(stream_source)
            if not cap.isOpened():
                raise ValueError(f"无法打开视频流: {stream_source}")
        else:
            cap = stream_source

        while True:
            # 获取帧数据
            if isinstance(cap, cv2.VideoCapture):
                ret, frame = cap.read()
                if not ret:
                    break
            elif isinstance(stream_source, Generator):
                frame_bytes = next(stream_source)
                frame = cv2.imdecode(np.frombuffer(frame_bytes, np.uint8), cv2.IMREAD_COLOR)
            else:
                frame = stream_source

            # 调整帧尺寸
            frame = cv2.resize(frame, frame_size)

            # 执行推理
            results = model.predict(
                source=frame,
                conf=conf_threshold,
                iou=iou_threshold,
                verbose=verbose
            )

            # 可视化结果
            processed_frame = results[0].plot() if results else frame

            # 计算并存储当前帧的健康数据
            if results and results[0].boxes:
                health_data = calculate_dental_health(results)

                if 'error' not in health_data:
                    frame_count += 1
                    decay_ratios.append(health_data['total_decay_ratio'])
                    plaque_ratios.append(health_data['plaque_ratio'])

                    # 更新缓存中的实时数据
                    cache_data = {
                        'frame_count': frame_count,
                        'decay_ratios': decay_ratios,
                        'plaque_ratios': plaque_ratios,
                        'last_update': time.time()
                    }
                    cache.set(REALTIME_DATA_CACHE_KEY, cache_data, 3600)  # 缓存1小时

            # 准备返回数据
            result_data = {
                'boxes': results[0].boxes.data.cpu().numpy() if results and results[0].boxes else None,
                'masks': results[0].masks.data.cpu().numpy() if results and results[0].masks else None,
                'names': model.names
            } if results else None

            yield (processed_frame, result_data)

    except Exception as e:
        print(f"流处理过程中发生错误: {str(e)}")
    finally:
        if isinstance(cap, cv2.VideoCapture):
            cap.release()

def calculate_dental_health(results):
    """动态适配模型输出的健康数据计算"""
    # 模型实际类别映射（根据调试输出调整）
    CLASS_MAP = {
        0: 'caries',  # 蛀牙
        1: 'cavity',  # 龋洞
        2: 'crack',  # 裂缝
        3: 'tooth',  # 牙齿
        4: 'plaque'  # 牙菌斑
    }

    # 初始化所有可能类别的面积计数器
    areas = {name: 0 for name in CLASS_MAP.values()}

    for result in results:
        if not hasattr(result, 'masks') or result.masks is None:
            continue

        masks = result.masks.data.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy()
        confidences = result.boxes.conf.cpu().numpy()

        for mask, cls, conf in zip(masks, classes, confidences):
            if conf < 0:  # 过滤低置信度检测
                continue

            mask = (mask > 0.5).astype(np.uint8)
            area = cv2.countNonZero(mask)
            class_name = CLASS_MAP.get(int(cls))
            if class_name:
                areas[class_name] += area

    # 校验牙齿区域
    total_tooth = areas['tooth']
    if total_tooth < 1:
        return {'error': '牙齿区域检测不足'}

    # 计算各类问题占比
    cavity_ratio = areas.get('cavity', 0) / total_tooth
    crack_ratio = areas.get('crack', 0) / total_tooth
    caries_ratio = areas.get('caries', 0) / total_tooth
    plaque_ratio = areas.get('plaque', 0) / total_tooth

    # 计算总蛀牙问题占比（龋洞+裂缝+蛀牙）
    total_decay_ratio = cavity_ratio + crack_ratio + caries_ratio

    # 健康评分（基于蛀牙问题总占比）
    health_score = calculate_health_score(total_decay_ratio)
    cleanliness_score = calculate_cleanliness_score(plaque_ratio)

    return {
        'cavity_ratio': cavity_ratio,
        'crack_ratio': crack_ratio,
        'caries_ratio': caries_ratio,
        'total_decay_ratio': total_decay_ratio,  # 新增总蛀牙问题占比
        'plaque_ratio': plaque_ratio,
        'health_score': health_score,
        'cleanliness_score': cleanliness_score,
        'total_tooth_area': total_tooth
    }

def calculate_health_score(total_decay_ratio):
    """更新后的健康评分标准（基于蛀牙问题总占比）"""
    if total_decay_ratio < 0.05:
        return "A"
    elif total_decay_ratio < 0.15:
        return "B"
    elif total_decay_ratio < 0.3:
        return "C"
    elif total_decay_ratio < 0.5:
        return "D"
    else:
        return "E"


# def calculate_health_score(total_decay_ratio):
#     """更新后的健康评分标准（基于蛀牙问题总占比）"""
#     if total_decay_ratio < 0.05:
#         return "A (非常健康)"
#     elif total_decay_ratio < 0.15:
#         return "B (健康)"
#     elif total_decay_ratio < 0.3:
#         return "C (轻度问题)"
#     elif total_decay_ratio < 0.5:
#         return "D (中度问题)"
#     else:
#         return "E (严重问题)"

def calculate_cleanliness_score(plaque_ratio):
    """清洁度评分（保持不变）"""
    if plaque_ratio < 0.05:
        return "A"
    elif plaque_ratio < 0.15:
        return "B"
    elif plaque_ratio < 0.3:
        return "C"
    elif plaque_ratio < 0.5:
        return "D"
    else:
        return "E"

    # def calculate_cleanliness_score(plaque_ratio):
    #     """清洁度评分（保持不变）"""
    #     if plaque_ratio < 0.05:
    #         return "优秀 (非常清洁)"
    #     elif plaque_ratio < 0.15:
    #         return "良好 (基本清洁)"
    #     elif plaque_ratio < 0.3:
    #         return "一般 (需要加强清洁)"
    #     elif plaque_ratio < 0.5:
    #         return "较差 (明显牙菌斑)"
    #     else:
    #         return "极差 (严重牙菌斑)"



    def calculate_final_scores():
        """计算最终评分（基于所有帧的平均值）"""
        cache_data = cache.get(REALTIME_DATA_CACHE_KEY)
        if not cache_data or not cache_data.get('frame_count', 0):
            return None

        decay_avg = sum(cache_data['decay_ratios']) / cache_data['frame_count']
        plaque_avg = sum(cache_data['plaque_ratios']) / cache_data['frame_count']

        health_score = calculate_health_score(decay_avg)
        cleanliness_score = calculate_cleanliness_score(plaque_avg)

        return {
            'total_decay_ratio': decay_avg,
            'plaque_ratio': plaque_avg,
            'health_score': health_score,
            'cleanliness_score': cleanliness_score,
            'frame_count': cache_data['frame_count']
        }