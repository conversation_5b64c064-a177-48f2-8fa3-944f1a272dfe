"""
Django settings for DjangoProject project.

Generated by 'django-admin startproject' using Django 4.2.18.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
# create database gx_day14 DEFAULT CHARSET utf8 COLLATE utf8_general_ci;创建数据库
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-zo&%gk-ycn(yaz5k-s7(%(hzsmwa!q%@mcgef$&9@3^t1pfenk"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    'corsheaders',
    "dental",
]

# MIDDLEWARE = [
#     "django.middleware.security.SecurityMiddleware",
#     "django.contrib.sessions.middleware.SessionMiddleware",
#     "django.middleware.common.CommonMiddleware",
#     "django.middleware.csrf.CsrfViewMiddleware",
#     "django.contrib.auth.middleware.AuthenticationMiddleware",
#     "django.contrib.messages.middleware.MessageMiddleware",
#     "django.middleware.clickjacking.XFrameOptionsMiddleware",
#     'corsheaders.middleware.CorsMiddleware',
#     'django.middleware.common.CommonMiddleware',
# ]
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]
# settings.py

# 允许所有来源（开发环境用）

# 或者更安全的配置
CORS_ALLOWED_ORIGINS = [
    "http://localhost:6570",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://dental.sv6.tunnelfrp.com",
    'http://localhost:6559',
    'http://127.0.0.1:6559',
]
# 允许的请求方法
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# 允许的请求头
CORS_ALLOW_HEADERS = ('*')

# 允许携带凭证
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:6570",
    "http://localhost:80",
]

ROOT_URLCONF = "DjangoProject.urls"


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        'DIRS': [os.path.join(BASE_DIR, 'tooth-project/dist')],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "DjangoProject.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.sqlite3",
#         "NAME": BASE_DIR / "db.sqlite3",
#     }
# }

AUTH_USER_MODEL = 'dental.CustomUser'
# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.mysql",
#         "NAME": "dental",
#         "USER":"root",
#         "PASSWORD":"Tst761943@",
#         "HOST":"localhost",
#         "PORT":"3306",
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#         },
#     }
# }

from dj_db_conn_pool.backends.mysql import DatabaseWrapper as PooledMySQLWrapper

DATABASES = {
    "default": {
        "ENGINE": "dj_db_conn_pool.backends.mysql",
        "NAME": "dental",
        "USER": "root",
        "PASSWORD": "Tst761943@",
        "HOST": "localhost",
        "PORT": "3306",
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
        'POOL_OPTIONS': {
            'POOL_SIZE': 1000,  # 连接池的初始大小
            'MAX_OVERFLOW': 1000,  # 连接池允许的最大连接数
            'RECYCLE': 3600,  # 连接的最大生命周期（秒）
        }
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTH_USER_MODEL = 'dental.CustomUser'
# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/


STATIC_URL = "static/"
STATIC_ROOT=os.path.join(BASE_DIR,'static')
STATICFILES_DIR=[os.path.join(BASE_DIR,'tooth-project/dist/static'),]
# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.qq.com'  # QQ 邮箱的 SMTP 服务器地址
EMAIL_PORT = 465  # QQ 邮箱的 SMTP 端口（SSL）
EMAIL_USE_SSL = True  # 使用 SSL 加密
EMAIL_HOST_USER = '<EMAIL>'  # 您的 QQ 邮箱地址
EMAIL_HOST_PASSWORD = 'yncxmwycqbwddcch'  # QQ 邮箱的授权码
DEFAULT_FROM_EMAIL = '<EMAIL>'  # 默认发件人邮箱


AI_API_KEY = "sk-jxjjtuwgssuxnsbzbutueyvpkwhcbjcqdcgfjecelcwnlgvg"  # 替换为你的实际 API 密钥
# AI_API_URL = "https://api.siliconflow.cn/v1/chat/completions"


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",  # 使用1号数据库
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SOCKET_CONNECT_TIMEOUT": 5,
        },
        "KEY_PREFIX": "dental_",
    }
}
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 实时检测缓存键
REALTIME_DATA_CACHE_KEY = 'realtime_dental_data'
CACHE_TIMEOUT = 3600  # 1小时
# 增加上传数据大小限制
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB