from django.contrib import admin
from django.urls import path,include

from django.conf import settings
from django.conf.urls.static import static

from dental import views
from dental.views import ai_chat
from rest_framework.routers import DefaultRouter
router = DefaultRouter()
urlpatterns = [
    # path('admin/', admin.site.urls),
    path('api/upload_img',views.upload_image),
    path('api/get_result',views.get_result),
    path('api/ai_chat/', views.ai_chat),
    path('api/get_dental_data/',views.get_dental_data),
    path('api/get_dental_records/',views.get_dental_records),
    path('api/register/', views.register),#注册登录
    path('api/send-verification-code/',views.send_verification_code),#发送信息
    path('api/login/', views.user_login),
    path('api/education/content/', views.get_education_content),  # For POST requests
    path('api/start_stream', views.start_stream),
    path('api/stop_stream', views.stop_stream),
    path('api/video_feed', views.video_feed),

]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
