# Generated by Django 4.2.18 on 2025-04-29 10:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dental", "0004_dentalresult_caries_ratio_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="dentalresult",
            name="duration",
            field=models.FloatField(default=0, verbose_name="检测时长(秒)"),
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="frame_count",
            field=models.IntegerField(default=0, verbose_name="检测帧数"),
        ),
        migrations.AddField(
            model_name="dentalresult",
            name="is_stream_result",
            field=models.BooleanField(default=False, verbose_name="是否实时检测结果"),
        ),
    ]
