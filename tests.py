import os
from flask import Flask, request, Response, jsonify
import base64
import requests
from flask_cors import CORS
from PIL import Image
from io import BytesIO

app = Flask(__name__)
CORS(app)


class StylizeFaceView:
    def post(self):
        # 获取base64编码的图片数据
        base64_image = request.json.get('image')
        cloth_style = request.json.get("style")

        try:
            # 处理图片并返回base64编码
            image_base64 = self.dealImage(cloth_style, base64_image)

            # 将 Base64 数据保存为本地图片
            file_path = self.save_base64_to_file(image_base64)

            # 加载底层图片和透明 PNG 图片
            background_image = Image.open('output_images/output_image.png')  # 底层图片
            overlay_image = Image.open("output_images/kuang.png")  # 上层图片（透明 PNG 图片）

            # 获取底层图片和透明 PNG 图片的尺寸
            bg_width, bg_height = background_image.size
            overlay_width, overlay_height = overlay_image.size

            # 计算中心偏移量，使透明图片居中
            x_offset = (overlay_width - bg_width) // 2
            y_offset = (overlay_height - bg_height) // 2 + 31

            # 创建一个新的空白图像（大小与透明 PNG 图片一致），并粘贴底层图片
            final_image = Image.new("RGBA", (overlay_width, overlay_height), (0, 0, 0, 0))  # 使用 RGBA 模式，确保支持透明度
            final_image.paste(background_image, (x_offset, y_offset))  # 粘贴底层图片到合成图

            # 将透明图片叠加到合成图上，按中心位置粘贴
            final_image.paste(overlay_image, (0, 0), overlay_image)  # 使用透明通道
            # 将最终合成图保存为本地文件
            output_file_path = 'uploaded_images/output_image.png'
            final_image.save(output_file_path)

            # 使用 requests 将图片文件发送到后端服务器
            server_url = "http://110.42.240.223:2024/upload"  # 后端服务器地址
            with open(output_file_path, 'rb') as file:
                files = {'file': file}
                response = requests.post(server_url, files=files, verify=False)

            # 将最终合成图片转换为 Base64 字符串
            final_image_base64 = self.image_to_base64(final_image)

            # 返回处理结果的 Base64 编码
            return jsonify({"base64_image": final_image_base64})

        except Exception as e:
            # 其他未知错误，返回通用错误信息
            return jsonify({'error': f"处理图片时发生错误：{str(e)}"}), 500

    def dealImage(self, dynasty_data, img_base64):
        # 模拟的请求地址
        url = "http://127.0.0.1:7860"

        payload = {
            "init_images": [dynasty_data],
            "steps": 30,
            "denoising_strength": 0.01,
            "width": 1700,
            "height": 950,
            "inpainting_mask_invert": 1,
            "inpaint_full_res_padding": 1,
            "inpainting_fill": 1,
            "alwayson_scripts": {
                "roop": {"args": [img_base64, True]}
            }
        }

        response = requests.post(url=f'{url}/sdapi/v1/img2img', json=payload)
        r = response.json()

        # 返回图片的 Base64 编码
        return r["images"][0]

    def save_base64_to_file(self, base64_str):
        # 创建保存图片的目录
        save_dir = "output_images"
        os.makedirs(save_dir, exist_ok=True)

        # 定义保存文件路径
        file_path = os.path.join(save_dir, "output_image.png")

        # 将 Base64 解码为二进制数据并写入文件
        with open(file_path, "wb") as f:
            f.write(base64.b64decode(base64_str))

        return file_path

    def image_to_base64(self, image):
        # 将 PIL 图像转换为 Base64 字符串
        buffered = BytesIO()
        image.save(buffered, format="PNG")  # 保存为 PNG 格式
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return img_str


@app.route('/api/stylize_face', methods=['POST'])
def stylize_face():
    view = StylizeFaceView()
    return view.post()


if __name__ == '__main__':
    app.run(port=1121)
