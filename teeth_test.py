from ultralytics import YOL<PERSON>

def yolov8_seg_inference(model_path, image_path, conf_threshold=0.25, iou_threshold=0.45):

    # 加载 YOLOv8-seg 模型
    model = YOLO(model_path)

    # 进行推理
    results = model.predict(source=image_path, conf=conf_threshold, iou=iou_threshold)

    # 返回结果
    return results

if __name__ == "__main__":
    model_path = "D:\\software\\css\\django-projects\\DjangoProject\\best.pt"  # 模型路径
    image_path = r"D:\software\css\django-projects\DjangoProject\dentalai\test1\731_jpg.rf.197885effc29ca9bc5aeff24ad6e03a0.jpg" # 输入图像路径

    # 调用函数进行推理
    results = yolov8_seg_inference(model_path, image_path)

    # 打印结果
    for result in results:
        result.show()  # 显示结果图像