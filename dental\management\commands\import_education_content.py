import os
import django
import sys
from pathlib import Path
from django.core.management.base import BaseCommand
from dental.models import EducationContent
import json
from django.utils import timezone  # 正确的导入方式
from datetime import datetime  # 或者使用Python自带的datetime

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'DjangoProject.settings')
django.setup()


class Command(BaseCommand):
    help = 'Import education content from TXT files'

    def handle(self, *args, **options):
        # 使用绝对路径指向education_content目录
        project_root = Path(__file__).resolve().parent.parent.parent  # 项目根目录(DjangoProject/)
        content_dir = project_root / 'education_content'

        print(f"项目根目录: {project_root}")
        print(f"查找目录: {content_dir}")
        print(f"目录内容: {list(content_dir.glob('*')) if content_dir.exists() else '目录不存在'}")

        care_tips_file = content_dir / 'care_tips.txt'
        dental_facts_file = content_dir / 'dental_facts.txt'

        if care_tips_file.exists():
            self.process_file(care_tips_file, 'CARE_TIPS')
        else:
            self.stdout.write(self.style.ERROR(f'文件不存在: {care_tips_file}'))
            if content_dir.exists():
                self.stdout.write(f"目录内容: {[f.name for f in content_dir.iterdir()]}")

        if dental_facts_file.exists():
            self.process_file(dental_facts_file, 'DENTAL_FACTS')
        else:
            self.stdout.write(self.style.ERROR(f'文件不存在: {dental_facts_file}'))

    def process_file(self, file_path, content_type):
        try:
            self.stdout.write(f"\n正在处理文件: {file_path}")
            self.stdout.write(f"文件是否存在: {file_path.exists()}")

            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 修正1：使用正确的datetime获取方式
            file_timestamp = datetime.fromtimestamp(file_path.stat().st_mtime)
            # 或者使用Django的timezone：
            # from django.utils import timezone
            # file_timestamp = timezone.make_aware(datetime.fromtimestamp(file_path.stat().st_mtime))

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.stdout.write(f"文件大小: {len(content)} 字节")

            items = json.loads(content)
            self.stdout.write(f"成功解析JSON，找到 {len(items)} 个项目")

            deleted_count, _ = EducationContent.objects.filter(content_type=content_type).delete()
            self.stdout.write(f"已删除 {deleted_count} 条旧记录")

            created_count = 0
            for item in items:
                EducationContent.objects.create(
                    content_type=content_type,
                    title=item.get('title', '未命名'),
                    icon=item.get('icon', '💡'),
                    description=item.get('description', ''),
                    content=item.get('content', ''),
                    details=item.get('details', []),
                    source_file=str(file_path),
                    file_timestamp=file_timestamp,  # 使用修正后的时间戳
                    is_active=True
                )
                created_count += 1

            self.stdout.write(self.style.SUCCESS(f'成功导入 {created_count} 条 {content_type} 数据'))

        except json.JSONDecodeError as e:
            self.stdout.write(self.style.ERROR(f'JSON 解析失败: {str(e)}'))
            if hasattr(e, 'doc'):
                self.stdout.write(f"错误上下文: {e.doc[e.pos - 30:e.pos + 30]}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'处理文件时发生错误: {str(e)}'))
            import traceback
            traceback.print_exc(file=sys.stdout)