import requests
import concurrent.futures
import time

# 定义请求的 URL
url = 'http://127.0.0.1:8000/ai_chat/'

# 定义要发送的消息
message = {'message': '这是一个测试消息'}

# 定义一个函数来发送单个请求
def send_request():
    try:
        response = requests.post(url, json=message)
        response.raise_for_status()  # 检查响应状态码
        return response.status_code
    except requests.RequestException as e:
        print(f"请求出错: {e}")
        return None

# 定义压测函数
def stress_test(num_requests, num_threads):
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有请求到线程池
        futures = [executor.submit(send_request) for _ in range(num_requests)]
        # 获取所有请求的结果
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    end_time = time.time()
    total_time = end_time - start_time

    # 统计成功和失败的请求数量
    success_count = sum(1 for result in results if result == 200)
    failure_count = num_requests - success_count

    # 打印压测结果
    print(f"总请求数: {num_requests}")
    print(f"成功请求数: {success_count}")
    print(f"失败请求数: {failure_count}")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"平均响应时间: {total_time / num_requests:.2f} 秒")

if __name__ == "__main__":
    # 定义要发送的请求数量和并发线程数
    num_requests = 100  # 总请求数
    num_threads = 10    # 并发线程数
    stress_test(num_requests, num_threads)